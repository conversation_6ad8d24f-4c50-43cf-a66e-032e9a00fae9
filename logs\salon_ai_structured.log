{"timestamp": 1755102493.8328943, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102493.8336601, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "preloading plugins", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google", "av"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.0020409, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 42, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.005474, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.0081398, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 46, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.010628, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 48, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8251731, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 42, "elapsed_time": 0.82, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8251998, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8253386, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8250575, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.828035, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 44, "elapsed_time": 0.82, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8287878, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 48, "elapsed_time": 0.82, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8956525, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 46, "elapsed_time": 0.89, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102496.8955934, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102498.4758627, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "id": "AW_dWJW4FE9oGMu", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US Central", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102534.3026555, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 492, "message": "draining worker", "id": "AW_dWJW4FE9oGMu", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102534.3051038, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 575, "message": "shutting down worker", "id": "AW_dWJW4FE9oGMu", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102551.9890008, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102551.9897075, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "preloading plugins", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google", "av"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.0016136, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 42, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.0039988, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.0067453, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 46, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.0099366, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 48, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7654135, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 44, "elapsed_time": 0.76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7653525, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7664955, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 46, "elapsed_time": 0.76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.765621, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7800422, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 42, "elapsed_time": 0.78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7800848, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7800078, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102554.7810597, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 48, "elapsed_time": 0.77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755102556.3062174, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "id": "AW_4MugqFxRgXdd", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US Central", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103112.6248293, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103112.6288636, "level": "DEV", "logger": "livekit.agents", "module": "watcher", "function": "run", "line": 84, "message": "Watching D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent", "taskName": "Task-1", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103116.1115777, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103116.116721, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "taskName": "agent_runner", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103117.5869057, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_DGxoEeMzJDu6", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US Central", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103117.873479, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "taskName": "Task-14", "job_id": "AJ_zXtyUwDGc5rv", "dispatch_id": "", "room_name": "playground-r61U-9si4", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103118.1372485, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-15", "tid": 31028, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103118.1384208, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-15", "tid": 31028, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103118.1384208, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103118.1412778, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103118.7153215, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103118.7163239, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103127.315234, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\opentelemetry\\util\\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\ipc\\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\ipc\\job_proc_lazy_main.py:260]> took too long: 2.03 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103127.3172345, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "taskName": "_room_io_start", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103128.1712394, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103129.4374425, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "taskName": "Task-49", "participant": "identity-ABW2", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103129.43944, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103129.4404414, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103130.8937902, "level": "INFO", "logger": "google_genai.live", "module": "live", "function": "connect", "line": 1053, "message": "b'{\\n  \"setupComplete\": {}\\n}\\n'", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103157.2628186, "level": "DEBUG", "logger": "livekit.agents", "module": "generation", "function": "_execute_tools_task", "line": 496, "message": "executing tool", "taskName": "execute_tools_task", "arguments": "{}", "speech_id": "speech_9ddfd5d7ee38", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103157.2649512, "level": "DEBUG", "logger": "livekit.agents", "module": "generation", "function": "_execute_tools_task", "line": 575, "message": "tools execution completed", "taskName": "execute_tools_task", "speech_id": "speech_9ddfd5d7ee38", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103168.835554, "level": "INFO", "logger": "livekit.agents", "module": "utils", "function": "log_metrics", "line": 25, "message": "RealtimeModel metrics", "taskName": "gemini-realtime-recv", "ttft": 0.47, "input_tokens": 2919, "cached_input_tokens": 0, "output_tokens": 58, "total_tokens": 2977, "tokens_per_second": 5.16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103170.9254756, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "taskName": "Task-49", "participant": "identity-ABW2", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103170.9466383, "level": "INFO", "logger": "livekit.agents", "module": "room_io", "function": "_on_participant_disconnected", "line": 430, "message": "closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)", "taskName": "Task-29", "participant": "identity-ABW2", "reason": "CLIENT_INITIATED", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103170.958983, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "taskName": "Task-181", "participant": "identity-ABW2", "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103170.9636853, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_send_task", "line": 689, "message": "send task finished.", "taskName": "gemini-realtime-send", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103171.2456496, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "taskName": "Task-181", "reason": "participant_disconnected", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103172.2448983, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103172.251382, "level": "DEBUG", "logger": "livekit.agents", "module": "job_thread_executor", "function": "_monitor_task", "line": 278, "message": "job exiting", "taskName": "Task-21", "reason": "", "tid": 31028, "job_id": "AJ_zXtyUwDGc5rv", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755103172.2518737, "level": "WARNING", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::rtc_engine:453:livekit::rtc_engine - received session close: \"signal client closed: \\\"stream closed\\\"\" UnknownReason Resume", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
