{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "loki"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"displayMode": "visible", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "sum by (level) (count_over_time({job=\"salon-ai-structured\"} [$__interval]))", "queryType": "", "refId": "A"}], "title": "Log Levels Distribution", "type": "piechart"}, {"datasource": {"type": "loki", "uid": "loki"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "sum by (level) (rate({job=\"salon-ai-structured\"} [$__rate_interval]))", "queryType": "", "refId": "A"}], "title": "Log Rate by Level", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "loki"}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "{job=\"salon-ai-structured\"} |= ``", "queryType": "", "refId": "A"}], "title": "Recent Logs", "type": "logs"}, {"datasource": {"type": "loki", "uid": "loki"}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 4, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "{job=\"salon-ai-structured\"} |= \"ERROR\"", "queryType": "", "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "type": "logs"}, {"datasource": {"type": "loki", "uid": "loki"}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 5, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "editorMode": "code", "expr": "{job=\"salon-ai-structured\"} |= \"WARNING\"", "queryType": "", "refId": "A"}], "title": "Warning Logs", "type": "logs"}], "refresh": "5s", "schemaVersion": 39, "tags": ["salon-ai", "logs", "loki"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Salon AI - Logs Dashboard", "uid": "salon-ai-logs", "version": 1, "weekStart": ""}